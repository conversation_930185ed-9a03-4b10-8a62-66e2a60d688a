package testergame

import (
	"context"
	"encoding/json"
	"os"
	"slices"
	"sync"
	"time"

	"hungrystudio.com/datatester/models/release"

	"hungrystudio.com/datatester/models/distributes"

	"github.com/Masterminds/semver/v3"
	"golang.org/x/exp/maps"
)

type BucketConfig struct {
	BucketId           int                                  `json:"bucketId"`
	BucketTitle        string                               `json:"bucketTitle"`
	BundleId           string                               `json:"bundleId"`
	BucketType         string                               `json:"bucketType"`
	BucketState        string                               `json:"bucketState"`
	BucketDesc         string                               `json:"bucketDesc"`
	MinResVersion      string                               `json:"minResVersion"`
	MinSdkVersion      string                               `json:"minSDKVersion"`
	Countries          map[string]int                       `json:"countries"`   // 允许国家
	NoCountries        map[string]int                       `json:"noCountries"` // 不允许国家
	MaxFlowNum         int                                  `json:"maxFlowNum"`
	HourMaxFlowNum     int                                  `json:"hourMaxFlowNum"`
	FlowWeightPercent  int                                  `json:"flowWeightPercent"`
	ExperimentList     map[string]map[string]any            `json:"experimentList"`
	ExpermentDays      int                                  `json:"experimentDays"`     // 实验有效天数，针对新用户
	ReleaseExperiments map[string]ReleaseConfig             `json:"releaseExperiments"` // 释放实验配置
	ReleaseSettings    map[string]*ReleaseSetting           `json:"releaseSettings"`    // 实验释放设置 实验释放配置升级版本
	ExperimentType     int                                  `json:"experimentType"`     // 实验类型 0 新增， 1 活跃
	CloseReset         int                                  `json:"closeReset"`         // 桶停量重新分配开关
	AIFeatures         map[string]map[string]map[string]any `json:"aiFeatures"`         // ai特征，格式：map[expId]map[featureId]特征map
	Conditions         *release.Release                     `json:"conditions"`

	IndependentAim      map[string]any `json:"independentAim"`      // 独立定向
	TotalExperimentList []any          `json:"totalExperimentList"` // 原方案号【未经历缩减/扩圈之前】
	AimBucketId         int            `json:"aimBucketId"`         // 定向桶
	AimExperimentId     string         `json:"aimExperimentId"`     // 定向目标方案
	ABTestProjectKey    string         `json:"abtestProjectKey"`    // ABTest项目Key
	OneWayReleaseTag    int64          `json:"oneWayReleaseTag"`    //进桶tag
}

// 实验类型
const (
	ExperimentTypeNew    int = iota // 新增实验类型
	ExperimentTypeActive            // 活跃实验类型
)

// 桶类型
const (
	BucketTypeNormal      string = "normal"
	BucketTypeClientPanel string = "client_panel"
	BucketTypeOld         string = "old"
	BucketTypeAI          string = "ai"
	BucketTypeABTest      string = "abtest"
)

const (
	CloseResetClosed int = iota
	CloseResetOpen
)

// GameTesterCacheKey 配置Redis Key
const GameTesterCacheKey string = "hs:gametester:config"

// GameTesterCacheKeyPrefix 包配置前缀
const GameTesterCacheKeyPrefix string = "hs:gametester:config:"

// GameTesterCacheExpire Redis缓存时间
const GameTesterCacheExpire = 24 * time.Hour * 365

// GameTesterUpdateInterval 配置更新间隔
const GameTesterUpdateInterval = time.Minute * 1

// VirtualBucketId 虚拟BucketId，用在旧方案上
const VirtualBucketId int = 0

// ClientPanelBucketId 客户端兜底BucketId
const ClientPanelBucketId int = 9999999999

// OldNewExperimentDadys 老用户新增转活跃天数
const OldNewExperimentDadys int64 = 24 * 60 * 60 * 14 * 1000

// Release 释放常量
const (
	ReleaseHoldBucket int = -1 // 释放保留对应的桶值
	ReleaseGlobal     int = -2 // 释放到大盘全局
)

const (
	BucketStateOnline  string = "online"  // 上线
	BucketStateOffline string = "offline" // 下线
	BucketStateClose   string = "close"   // 关闭
)

const (
	BucketCountryInvalid int = iota
	BucketCountryValid
)

var OldBuckets = []int{1, 2, 3}

type AIBucketIndex struct {
	schemes map[string]map[string]any
}

type BucketConfigs struct {
	bundleId           string                            // 包
	configs            []BucketConfig                    // 所有配置
	bucketIndexes      map[string]map[int]BucketIndex    // bucket索引：包名, 桶ID，桶索引
	newBuckets         map[string][]int                  // 新增桶列表
	activeBuckets      map[string][]int                  // 活跃桶列表
	clientPanelBuckets map[string][]int                  // 客户端兜底桶
	aiBuckets          map[string]map[string]map[int]int // AIbuckets桶
	abNew2Active       map[int]int                       // AB测试新增桶转活跃桶
	sync.RWMutex
}

// BucketIndex 桶索引
type BucketIndex struct {
	Index                int      // 数组索引下标
	Experiments          []string // 实验列表
	DisExperimentSchemes []distributes.Scheme
}

const ExperimentJsonFileTempate string = "data/experiments/%s/%d/%s.json"

func NewBucketConfigs(bundleId string, updateInterval int64) *BucketConfigs {
	bc := &BucketConfigs{
		bundleId:           bundleId,
		configs:            make([]BucketConfig, 0),
		bucketIndexes:      make(map[string]map[int]BucketIndex),
		newBuckets:         make(map[string][]int),
		activeBuckets:      make(map[string][]int),
		clientPanelBuckets: make(map[string][]int),
		abNew2Active:       make(map[int]int),
		aiBuckets:          make(map[string]map[string]map[int]int),
	}
	bc.loadConfigs()
	go bc.update(updateInterval)
	return bc
}

func (bc *BucketConfigs) getGameTesterCacheKey() string {
	return GetGameTesterCacheKeyWithBundleAndEnv(bc.bundleId, os.Getenv("ENV"))
}

const (
	EnvTesting = "testing" // 测试环境
	EnvProd    = "prod"    // 生产环境
	EnvGray    = "gray"    // 灰度环境
	EnvDev     = "dev"     // 开发环境
)

func getGameTesterCacheKeyWithBundle(bundleId string) string {
	key := GameTesterCacheKey
	if bundleId != "" {
		key += ":" + bundleId
	}
	return key
}

func GetGameTesterCacheKeyWithBundleAndEnv(bundleId, env string) string {
	key := getGameTesterCacheKeyWithBundle(bundleId)
	if env == EnvGray {
		key += ":" + env
	}
	return key
}

//func SaveBundleConfig(bundleConfigs []BucketConfig, bundleId, env string) error {
//	cacheKey := getGameTesterCacheKeyWithBundleAndEnv(bundleId, env)
//	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
//	defer cancel()
//	err := redisClient.Set(ctx, cacheKey, bundleConfigs, GameTesterCacheExpire).Err()
//	if err != nil {
//		return err
//	}
//	return nil
//}

func (bc *BucketConfigs) loadConfigs() {
	sugared.Infof("Update BucketConfigs Start")
	configCacheKey := bc.getGameTesterCacheKey()
	sugared.Infof("Update BucketConfigs load CacheKey: %s", configCacheKey)
	configsJson, err := redisClient.Get(context.Background(), configCacheKey).Result()
	if err != nil {
		sugared.Errorf("loadConfigs redisClient.Get(%q): %v", bc.getGameTesterCacheKey(), err)
		return
	}
	var configs []BucketConfig
	err = json.Unmarshal([]byte(configsJson), &configs)
	if err != nil {
		sugared.Errorf("json.Unmarshal: %v", err)
		return
	}
	sugared.Infof("Update BucketConfigs lock")
	bc.Lock()
	defer bc.Unlock()
	// 下线的桶，不载入配置中
	bConfigs := make([]BucketConfig, 0)
	bucketIndexes := make(map[string]map[int]BucketIndex)
	newBuckets := make(map[string][]int)
	activeBuckets := make(map[string][]int)
	clientPanelBuckets := make(map[string][]int)
	sugared.Infof("Update BucketConfigs configs len:%d", len(configs))
	for index, config := range configs {
		if config.BucketState != BucketStateOffline {
			//sugared.Infof("BucketId: %d, BucketTitle: %s,BucketType: %s, BucketState:%s, BucketDesc: %s,ExperimentType:%d,Experiments: %v",
			//	config.BucketId, config.BucketTitle,
			//	config.BucketType, config.BucketState,
			//	config.BucketDesc, config.ExperimentType,
			//	maps.Keys(config.ExperimentList),
			//)
			// experimentList := make(map[string]map[string]any, 0)
			// for experimentId, experiment := range config.ExperimentList {
			// 	experimentList[experimentId] = nil
			// 	bc.saveExperiment(config.BundleId, config.BucketId, experimentId, experiment)
			// }
			// config.ExperimentList = experimentList
			if config.NoCountries == nil {
				config.NoCountries = make(map[string]int)
			}
			for cKey, cValue := range config.Countries {
				if cValue == BucketCountryInvalid {
					config.NoCountries[cKey] = cValue
					delete(config.Countries, cKey)
				}
			}
			// 解析条件
			config.Conditions = parseBucketConditions(config)
			// 生成ReleaseSettings索引
			if len(config.ReleaseSettings) > 0 {
				for sIndex, setting := range config.ReleaseSettings {
					setting.makeWeights()
					setting.makeTraffics()
					config.ReleaseSettings[sIndex] = setting
				}
				sugared.Infof("ReleaseSettings: %+v", config.ReleaseSettings)
			}
			sugared.Infof("Bucket.Conditions: %+v", config.Conditions)
			bConfigs = append(bConfigs, config)

			// 填充桶索引
			if _, ok := bucketIndexes[config.BundleId]; !ok {
				bucketIndexes[config.BundleId] = make(map[int]BucketIndex)
			}
			exps := maps.Keys(config.ExperimentList)
			slices.Sort(exps)
			expSchemes := make([]distributes.Scheme, 0)
			for _, exp := range exps {
				expSchemes = append(expSchemes, distributes.Scheme{Name: exp})
			}
			bucketIndexes[config.BundleId][config.BucketId] = BucketIndex{Index: index, Experiments: exps, DisExperimentSchemes: expSchemes}

			// 填充所有新增实验和活跃实验列表，方便处理当前可以分流的新增和活跃桶
			if _, ok := newBuckets[config.BundleId]; !ok {
				newBuckets[config.BundleId] = make([]int, 0)
			}
			if _, ok := activeBuckets[config.BundleId]; !ok {
				activeBuckets[config.BundleId] = make([]int, 0)
			}
			if config.BucketType == BucketTypeNormal || config.BucketType == BucketTypeAI || config.BucketType == BucketTypeABTest {
				if config.BucketState != BucketStateOnline {
					continue
				}
				if len(config.ExperimentList) == 0 && config.BucketType != BucketTypeABTest {
					continue
				}
				switch config.ExperimentType {
				case ExperimentTypeNew:
					newBuckets[config.BundleId] = append(newBuckets[config.BundleId], config.BucketId)
				case ExperimentTypeActive:
					activeBuckets[config.BundleId] = append(activeBuckets[config.BundleId], config.BucketId)
				}
			}

			// 填充客户端兜底桶列表
			if config.BucketType == BucketTypeClientPanel {
				if _, ok := clientPanelBuckets[config.BundleId]; !ok {
					clientPanelBuckets[config.BundleId] = make([]int, 0)
				}
				clientPanelBuckets[config.BundleId] = append(clientPanelBuckets[config.BundleId], config.BucketId)
			}

			// ai桶索引处理
			if config.BucketType == BucketTypeAI {
				if _, ok := bc.aiBuckets[config.BundleId]; !ok {
					bc.aiBuckets[config.BundleId] = make(map[string]map[int]int)
				}
				aiBucket := make(map[string]map[int]int)
				for expId, _ := range config.AIFeatures {
					bc.aiBuckets[config.BundleId][expId] = make(map[int]int)
					if exp, ok := config.ExperimentList[expId]; ok {
						if _, ok = aiBucket["expId"]; !ok {
							aiBucket["expId"] = make(map[int]int)
						}
						if featuresAny, ok := exp["features"]; ok {
							if features, ok := featuresAny.([]map[string]any); ok {
								for fIndex, feature := range features {
									if fIdAny, ok := feature["id"]; ok {
										if fIdFloat, ok := fIdAny.(float64); ok {
											fIdInt := int(fIdFloat)
											aiBucket[expId][fIdInt] = fIndex
											bc.aiBuckets[config.BundleId][expId][fIndex] = fIdInt
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	bc.configs = bConfigs
	bc.bucketIndexes = bucketIndexes
	bc.newBuckets = newBuckets
	bc.activeBuckets = activeBuckets
	bc.clientPanelBuckets = clientPanelBuckets
	//bc.resetNew2Active()
	DumpBucketConfigs(bc.configs)
	//redisClient.Expire(context.Background(), bc.getGameTesterCacheKey(), GameTesterCacheExpire)
	sugared.Infof("Update BucketConfigs Success")
}

func (bc *BucketConfigs) GetABActiveBucket(bucketConfig BucketConfig) (BucketConfig, bool) {
	bucketId := bucketConfig.AimBucketId
	nb, ok := bc.GetBucketConfig(bc.bundleId, bucketId)
	if !ok {
		sugared.Errorf("ABTest BucketId: %d, not found", bucketId)
		return BucketConfig{}, false
	}
	if nb.BucketType != BucketTypeABTest {
		sugared.Errorf("ABTest BucketId: %d, not ABTest", bucketId)
		return BucketConfig{}, false
	}
	return nb, true
}

func (bc *BucketConfigs) GetABNewBucket(bucketId int) (BucketConfig, bool) {
	bucketIndex, ok := bc.bucketIndexes[bc.bundleId][bucketId]
	if !ok {
		return BucketConfig{}, false
	}
	if bucketIndex.Index >= 0 && bucketIndex.Index < len(bc.configs) {
		return bc.configs[bucketIndex.Index], true
	}

	return BucketConfig{}, false

}
func (bc *BucketConfigs) resetNew2Active() {
	for _, bucketId := range bc.newBuckets[bc.bundleId] {
		bucketConfig, ok := bc.GetABNewBucket(bucketId)
		if !ok {
			continue
		}
		if bucketConfig.BucketType != BucketTypeABTest {
			continue
		}
		//find active bucket
		for _, activeBucketId := range bc.activeBuckets[bc.bundleId] {
			activeBucketConfig, ok := bc.GetABNewBucket(activeBucketId)
			if !ok {
				continue
			}
			if activeBucketConfig.BucketType != BucketTypeABTest {
				continue
			}

			if activeBucketConfig.MinResVersion != bucketConfig.MinResVersion ||
				activeBucketConfig.MinSdkVersion != bucketConfig.MinSdkVersion {
				continue
			}

			if len(activeBucketConfig.Countries) != len(bucketConfig.Countries) ||
				len(activeBucketConfig.NoCountries) != len(bucketConfig.NoCountries) {
				continue
			}
			bDiff := false
			for cKey, cValue := range bucketConfig.Countries {
				if cValue != activeBucketConfig.Countries[cKey] {
					bDiff = true
					break
				}
			}
			if bDiff {
				continue
			}
			for cKey, cValue := range bucketConfig.NoCountries {
				if cValue != activeBucketConfig.NoCountries[cKey] {
					bDiff = true
					break
				}
			}
			if bDiff {
				continue
			}
			bc.abNew2Active[bucketId] = activeBucketId
			break
		}
		if _, ok := bc.abNew2Active[bucketId]; !ok {
			sugared.Errorf("ABTest BucketId: %d, not found active bucket", bucketId)
		}
	}
}

// saveExperiment 保存实验配置
// func (bc *BucketConfigs) saveExperiment(bundleId string, bucketId int, experimentId string, experiment map[string]any) error {
// 	experimentJson, err := json.Marshal(experiment)
// 	if err != nil {
// 		sugared.Errorf("json.Marshal: %v", err)
// 		return err
// 	}
// 	filePath := fmt.Sprintf(ExperimentJsonFileTempate, bundleId, bucketId, experimentId)
// 	dir := filepath.Dir(filePath)
// 	if _, err = os.Stat(dir); err != nil {
// 		err = os.MkdirAll(dir, 0755)
// 		if err != nil {
// 			sugared.Errorf("os.MkdirAll: %v", err)
// 			return err
// 		}
// 	}

// 	err = os.WriteFile(fmt.Sprintf("%s/%s.json", dir, experimentId), experimentJson, 0644)
// 	if err != nil {
// 		sugared.Errorf("os.WriteFile: %v", err)
// 		return err
// 	}
// 	return nil
// }

// GetExperiment 获取实验配置
// func (bc *BucketConfigs) GetExperiment(bundleId string, bucketId int, experimentId string) (map[string]any, error) {
// 	filePath := fmt.Sprintf(ExperimentJsonFileTempate, bundleId, bucketId, experimentId)
// 	experimentJson, err := os.ReadFile(filePath)
// 	if err != nil {
// 		sugared.Errorf("os.ReadFile: %v", err)
// 		return nil, err
// 	}
// 	var experiment map[string]any
// 	err = json.Unmarshal(experimentJson, &experiment)
// 	if err != nil {
// 		sugared.Errorf("json.Unmarshal: %v", err)
// 		return nil, err
// 	}
// 	return experiment, nil
// }

// update 更新桶配置
func (bc *BucketConfigs) update(updateInterval int64) {
	ticker := time.NewTicker(time.Minute * time.Duration(updateInterval))
	defer ticker.Stop()
	for range ticker.C {
		bc.loadConfigs()
	}
}

// Save 保存实验配置
//func (bc *BucketConfigs) Save(bundleId string, jsonData []byte) error {
//	configs := make([]BucketConfig, 0)
//	err := json.Unmarshal(jsonData, &configs)
//	if err != nil {
//		return err
//	}
//	for _, config := range configs {
//		_, err := semver.NewVersion(config.MinResVersion)
//		if err != nil {
//			return fmt.Errorf("MinResVersion(%s), Error: %v", config.MinResVersion, err)
//		}
//		_, err = semver.NewVersion(config.MinSdkVersion)
//		if err != nil {
//			return fmt.Errorf("MinSdkVersion(%s), Error: %v", config.MinSdkVersion, err)
//		}
//	}
//	err = redisClient.Set(context.Background(), getGameTesterCacheKeyWithBundle(bundleId), jsonData, GameTesterCacheExpire).Err()
//	if err != nil {
//		sugared.Errorf("redisClient.Set: %v", err)
//		return err
//	}
//	if backupRedisClient != nil {
//		err = backupRedisClient.Set(context.Background(), getGameTesterCacheKeyWithBundle(bundleId), jsonData, GameTesterCacheExpire).Err()
//		if err != nil {
//			sugared.Errorf("backupRedisClient.Set: %v", err)
//		} else {
//			sugared.Infof("backupRedisClient Write bucketConfigs Success")
//		}
//	}
//	return nil
//}

// GetAll 获取所有桶配置
func (bc *BucketConfigs) GetAll() []BucketConfig {
	allConfigs := slices.Clone(bc.configs)
	for index, config := range allConfigs {
		expList := make(map[string]map[string]any)
		for expId := range config.ExperimentList {
			expList[expId] = nil
		}
		config.ExperimentList = expList
		allConfigs[index] = config
	}
	return allConfigs
}

func (bc *BucketConfigs) GetAllWithBundleId(bundleId string) ([]BucketConfig, error) {
	key := GetGameTesterCacheKeyWithBundleAndEnv(bundleId, os.Getenv("ENV"))
	configJson, err := redisClient.Get(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}
	configs := make([]BucketConfig, 0)
	err = json.Unmarshal([]byte(configJson), &configs)
	if err != nil {
		return nil, err
	}
	for index, config := range configs {
		expList := make(map[string]map[string]any)
		for expId := range config.ExperimentList {
			expList[expId] = nil
		}
		config.ExperimentList = expList
		configs[index] = config
	}
	return configs, nil
}

func (bc *BucketConfigs) GetAllConfigsWithBundleId(bundleId string) ([]BucketConfig, error) {
	key := GetGameTesterCacheKeyWithBundleAndEnv(bundleId, os.Getenv("ENV"))
	configJson, err := redisClient.Get(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}
	configs := make([]BucketConfig, 0)
	err = json.Unmarshal([]byte(configJson), &configs)
	if err != nil {
		return nil, err
	}
	return configs, nil
}

func (bc *BucketConfigs) GetAllNew() []BucketConfig {
	return bc.configs
}

// GetVirtualBucket 虚拟桶，用以适配老的实验方案
// func (bc *BucketConfigs) GetVirtualBucket(bundleId string) (BucketConfig, error) {
// 	for _, bConfig := range bc.configs {
// 		if bConfig.BundleId == bundleId && bConfig.BucketId == VirtualBucketId {
// 			return bConfig, nil
// 		}
// 	}
// 	return BucketConfig{}, fmt.Errorf("not found %s virtual bucket config", bundleId)
// }

// GetOldBucket 查找实验对应的老用户桶
func (bc *BucketConfigs) GetOldBucket(bundleId, experimentId string) (BucketConfig, bool) {
	bc.RLock()
	defer bc.RUnlock()
	for _, bConfig := range bc.configs {
		if bConfig.BundleId == bundleId && bConfig.BucketType == BucketTypeOld {
			if _, ok := bConfig.ExperimentList[experimentId]; ok {
				return bConfig, true
			}
			if _, ok := bConfig.ReleaseExperiments[experimentId]; ok {
				return bConfig, true
			}
		}
	}
	sugared.Errorf("not found bundle %s, experiment: %s, old bucket config", bundleId, experimentId)
	return BucketConfig{}, false
}

// GetClientPanelBucket 查找实验对应的客户端兜底桶
func (bc *BucketConfigs) GetClientPanelBucket(bundleId, experimentId string) (BucketConfig, bool) {
	bc.RLock()
	defer bc.RUnlock()
	if clientPanelBuckets, ok := bc.clientPanelBuckets[bundleId]; ok {
		for _, bucketId := range clientPanelBuckets {
			if bConfig, ok := bc.GetBucketConfig(bundleId, bucketId); ok {
				if _, ok := bConfig.ExperimentList[experimentId]; ok {
					return bConfig, true
				}
				if _, ok := bConfig.ReleaseExperiments[experimentId]; ok {
					return bConfig, true
				}
			}
		}
	}

	// for _, bConfig := range bc.configs {
	// 	if bConfig.BundleId == bundleId && bConfig.BucketType == BucketTypeClientPanel {
	// 		if _, ok := bConfig.ExperimentList[experimentId]; ok {
	// 			return bConfig, true
	// 		}
	// 		if _, ok := bConfig.ReleaseExperiments[experimentId]; ok {
	// 			return bConfig, true
	// 		}
	// 	}
	// }
	sugared.Errorf("not found bundle: %s, experiment: %s, client_panel bucket config", bundleId, experimentId)
	return BucketConfig{}, false
}

// GetBucketConfig 获取指定桶配置
func (bc *BucketConfigs) GetBucketConfig(bundleId string, bucketId int) (BucketConfig, bool) {
	bc.RLock()
	defer bc.RUnlock()
	if bucketIndex, ok := bc.bucketIndexes[bundleId][bucketId]; ok {
		if bucketIndex.Index >= 0 && bucketIndex.Index < len(bc.configs) {
			return bc.configs[bucketIndex.Index], true
		}
	}
	return BucketConfig{}, false
	// for _, bConfig := range bc.configs {
	// 	if bConfig.BundleId == bundleId && bConfig.BucketId == bucketId && bConfig.BucketState != BucketStateOffline {
	// 		return bConfig, true
	// 	}
	// }
	// return BucketConfig{}, false
}

// GetBucketExperiments 获取指定的桶的实验列表
func (bc *BucketConfigs) GetBucketExperiments(bundleId string, bucketId int) []string {
	bc.RLock()
	defer bc.RUnlock()
	if bucketIndex, ok := bc.bucketIndexes[bundleId][bucketId]; ok {
		return bucketIndex.Experiments
	}
	return nil
}

// GetBucketDisExperiments 获取桶实验的distributes.Scheme列表
func (bc *BucketConfigs) GetBucketDisExperiments(bundleId string, bucketId int) []distributes.Scheme {
	bc.RLock()
	defer bc.RUnlock()
	if bucketIndex, ok := bc.bucketIndexes[bundleId][bucketId]; ok {
		return bucketIndex.DisExperimentSchemes
	}
	return nil
}

// GetBucketConfigsWithExperimentType 根据实验类型获取桶列表
func (bc *BucketConfigs) GetBucketConfigsWithExperimentType(bundleId string, experimentType int) []BucketConfig {
	bc.RLock()
	defer bc.RUnlock()
	bConfigs := make([]BucketConfig, 0)
	for _, bConfig := range bc.configs {
		if bConfig.BundleId == bundleId && bConfig.ExperimentType == experimentType && bConfig.BucketState == BucketStateOnline {
			bConfigs = append(bConfigs, bConfig)
		}
	}
	return bConfigs
}

// GetBucketExperiment 获取桶实验配置
func (bc *BucketConfigs) GetBucketExperiment(bundleId string, bucketId int, experimentId string) (map[string]any, bool) {
	bc.RLock()
	defer bc.RUnlock()
	for _, bucketConfig := range bc.configs {
		if bucketConfig.BucketId == bucketId {
			if exp, ok := bucketConfig.ExperimentList[experimentId]; ok {
				// exp, err := bc.GetExperiment(bundleId, bucketId, experimentId)
				// if err != nil {
				// 	sugared.Errorf("bc.getExperiment(%d, %s): %v", bucketId, experimentId, err)
				// 	return nil, false
				// }
				return exp, true
			}
		}
	}
	return nil, false
}

// GetBucketConfigs 获取实验桶列表 作为实验分配用
func (bc *BucketConfigs) GetBucketConfigs(bundleId string, experimentType int, resVersion, sdkVersion, country string, oneWayReleaseTag int64) []BucketConfig {
	bc.RLock()
	defer bc.RUnlock()
	var buckets []int
	switch experimentType {
	case ExperimentTypeNew:
		buckets = bc.newBuckets[bundleId]
	case ExperimentTypeActive:
		buckets = bc.activeBuckets[bundleId]
	}
	if buckets == nil {
		sugared.Errorf("GetBucketConfigs: no experimentType: %d buckets", experimentType)
		return nil
	}
	bConfigs := make([]BucketConfig, 0)
	resV, err := semver.NewVersion(resVersion)
	if err != nil {
		sugared.Errorf("semver.NewVersion(%q): %v", resVersion, err)
		return nil
	}
	sdkV, err := semver.NewVersion(sdkVersion)
	if err != nil {
		sugared.Errorf("semver.NewVersion(%q): %v", sdkVersion, err)
		return nil
	}
	sugared.Infof("resVersion: %s, sdkVersion: %s", resVersion, sdkVersion)
	for _, bucketId := range buckets {
		// 直接访问，避免重复获取锁
		if bucketIndex, ok := bc.bucketIndexes[bundleId][bucketId]; ok {
			if bucketIndex.Index >= 0 && bucketIndex.Index < len(bc.configs) {
				bConfig := bc.configs[bucketIndex.Index]
				// 值针对normal类型的桶
				//if bConfig.BucketType != BucketTypeNormal {
				//	continue
				//}
				//if len(bConfig.ExperimentList) == 0 {
				//	continue
				//}
				cResV, err := semver.NewVersion(bConfig.MinResVersion)
				if err != nil {
					sugared.Errorf("semver.NewVersion(%q): %v", bConfig.MinResVersion, err)
					continue
				}
				cSdkV, err := semver.NewVersion(bConfig.MinSdkVersion)
				if err != nil {
					sugared.Errorf("semver.NewVersion(%q): %v", bConfig.MinSdkVersion, err)
					continue
				}
				sugared.Infof("Bundle: %s, BucketId: %d,ExperimentType: %d,ResVersion: %s,SDKVersion: %s,CResVersion:%s, CSDKVersion:%s", bConfig.BundleId, bConfig.BucketId, bConfig.ExperimentType, resVersion, sdkVersion, bConfig.MinResVersion, bConfig.MinSdkVersion)
				//if (resV.Equal(cResV) || resV.GreaterThan(cResV)) && (sdkV.Equal(cSdkV) || sdkV.GreaterThan(cSdkV)) {
				//	// 增加国家过滤 当国家不为空，并切桶中配置国家参数时，如果上传的国家参数，在桶设置的国家参数里，会进入桶
				//	if country != "" && len(bConfig.Countries) > 0 {
				//		if _, ok = bConfig.Countries[strings.ToUpper(country)]; ok {
				//			bConfigs = append(bConfigs, bConfig)
				//			continue
				//		}
				//	}
				//	sugared.Infof("ResVersion: %s,SDKVersion: %s,CResVersion:%s, CSDKVersion:%s Valid", resVersion, sdkVersion, bConfig.MinResVersion, bConfig.MinSdkVersion)
				//	bConfigs = append(bConfigs, bConfig)
				//}
				// 判断资源版本和sdk版本是否符合
				if resV.LessThan(cResV) || sdkV.LessThan(cSdkV) {
					continue
				}
				// 判断是否在可以进桶的国家里面
				if len(bConfig.Countries) > 0 {
					if _, ok = bConfig.Countries[country]; !ok {
						continue
					}
				}
				// 判断是是否在在不仅桶国家内
				if len(bConfig.NoCountries) > 0 {
					if _, ok = bConfig.NoCountries[country]; ok {
						continue
					}
				}

				//判断进桶标签
				if bConfig.OneWayReleaseTag > 0 && bConfig.OneWayReleaseTag != oneWayReleaseTag {
					continue
				}
				bConfigs = append(bConfigs, bConfig)
				//if len(bConfig.Countries) == 0 {
				//	bConfigs = append(bConfigs, bConfig)
				//	continue
				//} else {
				//	if _, ok = bConfig.Countries[strings.ToUpper(country)]; ok {
				//		bConfigs = append(bConfigs, bConfig)
				//	}
				//}
			}
		}
	}
	// for _, bConfig := range bc.configs {
	// 	if bConfig.BundleId == bundleId && bConfig.ExperimentType == experimentType {
	// 		// 值针对normal类型的桶
	// 		if bConfig.BucketType != BucketTypeNormal {
	// 			continue
	// 		}
	// 		if len(bConfig.ExperimentList) == 0 {
	// 			continue
	// 		}
	// 		cResV, err := semver.NewVersion(bConfig.MinResVersion)
	// 		if err != nil {
	// 			sugared.Errorf("semver.NewVersion(%q): %v", bConfig.MinResVersion, err)
	// 			continue
	// 		}
	// 		cSdkV, err := semver.NewVersion(bConfig.MinSdkVersion)
	// 		if err != nil {
	// 			sugared.Errorf("semver.NewVersion(%q): %v", bConfig.MinSdkVersion, err)
	// 			continue
	// 		}
	// 		sugared.Infof("ResVersion: %s,SDKVersion: %s,CResVersion:%s, CSDKVersion:%s", resVersion, sdkVersion, bConfig.MinResVersion, bConfig.MinSdkVersion)
	// 		if (resV.Equal(cResV) || resV.GreaterThan(cResV)) && (sdkV.Equal(cSdkV) || sdkV.GreaterThan(cSdkV)) {
	// 			sugared.Infof("ResVersion: %s,SDKVersion: %s,CResVersion:%s, CSDKVersion:%s Valid", resVersion, sdkVersion, bConfig.MinResVersion, bConfig.MinSdkVersion)
	// 			bConfigs = append(bConfigs, bConfig)
	// 		}
	// 	}
	// }
	if len(bConfigs) > 0 {
		return bConfigs
	}
	return nil
}

func (bc *BucketConfigs) GetAIFeatureIndexes(bundleId, expId string) map[int]int {
	if featruesIndex, ok := bc.aiBuckets[bundleId][expId]; ok {
		return featruesIndex
	}
	return nil
}

// GetBucketExperimentDaysTime 获取实验有效天数时间戳
func (bc *BucketConfigs) GetBucketExperimentDaysTime(bucketConfig BucketConfig) int64 {
	return int64(bucketConfig.ExpermentDays * 24 * 60 * 60 * 1000)
}

func DumpBucketConfigs(bucketConfigs []BucketConfig) {
	for _, bucketConfig := range bucketConfigs {
		sugared.Infof("Bundle: %s, ID: %d, Title: %s,Type: %s, State: %s,MiniResVersion: %s,MiniSDKVersion: %s,Countries: %v, NoCountries: %v,MaxFlow: %d, HourMaxFlow: %d,CloseReset: %d, Conditions: %+v,Release: %+v,ReleaseSettings: %+v, OneWayReleaseTag: %d",
			bucketConfig.BundleId,
			bucketConfig.BucketId,
			bucketConfig.BucketTitle,
			bucketConfig.BucketType,
			bucketConfig.BucketState,
			bucketConfig.MinResVersion,
			bucketConfig.MinSdkVersion,
			bucketConfig.Countries,
			bucketConfig.NoCountries,
			bucketConfig.MaxFlowNum,
			bucketConfig.HourMaxFlowNum,
			bucketConfig.CloseReset,
			bucketConfig.Conditions,
			bucketConfig.ReleaseExperiments,
			bucketConfig.ReleaseSettings,
			bucketConfig.OneWayReleaseTag,
		)
	}
}

func parseBucketConditions(bucketConfig BucketConfig) *release.Release {
	conditions := make([]release.Condition, 0)
	minResCondtion := release.Condition{
		Key:           "resVersion",
		Op:            release.GreaterThanOrEqual,
		LogicOperator: release.AND,
		Value:         bucketConfig.MinResVersion,
		Type:          release.STRING,
		Method:        release.SemverCompare,
	}
	conditions = append(conditions, minResCondtion)
	minSDKCondtion := release.Condition{
		Key:           "sdkVersion",
		Op:            release.GreaterThanOrEqual,
		LogicOperator: release.AND,
		Value:         bucketConfig.MinSdkVersion,
		Type:          release.STRING,
		Method:        release.SemverCompare,
	}
	conditions = append(conditions, minSDKCondtion)
	if len(bucketConfig.Countries) > 0 {
		countriesCondtion := release.Condition{
			Key:           "country",
			Op:            release.IN,
			LogicOperator: release.AND,
			Value:         maps.Keys(bucketConfig.Countries),
			Type:          release.STRING,
		}
		conditions = append(conditions, countriesCondtion)
	}
	if len(bucketConfig.NoCountries) > 0 {
		countriesCondtion := release.Condition{
			Key:           "country",
			Op:            release.NotIn,
			LogicOperator: release.AND,
			Value:         maps.Keys(bucketConfig.NoCountries),
			Type:          release.STRING,
		}
		conditions = append(conditions, countriesCondtion)
	}

	if bucketConfig.OneWayReleaseTag > 0 {
		value := []int64{}
		value = append(value, bucketConfig.OneWayReleaseTag)
		countriesCondtion := release.Condition{
			Key:           "oneWayReleaseTag",
			Op:            release.IN,
			LogicOperator: release.AND,
			Value:         value,
			Type:          release.NUMBER,
		}
		conditions = append(conditions, countriesCondtion)
	}
	filter := release.Filter{
		Conditions: conditions,
	}
	return &release.Release{
		Filters:           []release.Filter{filter},
		TrafficAllocation: make([]release.TrafficAllocation, 0),
	}
}
